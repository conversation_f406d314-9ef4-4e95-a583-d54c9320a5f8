import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
from tkinterdnd2 import DND_FILES, TkinterDnD
import threading
import os
import tempfile
import time
import logging
from PIL import Image, ImageTk, ImageGrab
import pyperclip

# 尝试导入OCR库
OCR_ENGINE = None
try:
    import easyocr
    OCR_ENGINE = "easyocr"
    print("使用EasyOCR引擎")
except ImportError:
    try:
        from paddleocr import PaddleOCR
        OCR_ENGINE = "paddleocr"
        print("使用PaddleOCR引擎")
    except ImportError:
        print("未找到OCR引擎，请安装 easyocr 或 paddleocr")


class OCRProcessor:
    """OCR处理器类 - 支持多种OCR引擎"""

    def __init__(self):
        self.ocr = None
        self.is_initialized = False
        self.init_start_time = None
        self.engine_type = OCR_ENGINE
        
        # 配置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

    def initialize_ocr(self, callback=None):
        """异步初始化OCR引擎"""
        def init_worker():
            self.init_start_time = time.time()
            self.logger.info(f"开始初始化OCR引擎 ({self.engine_type})...")
            
            try:
                if self.engine_type == "easyocr":
                    self.logger.info("使用EasyOCR初始化...")
                    # EasyOCR支持中英文
                    self.ocr = easyocr.Reader(['ch_sim', 'en'], gpu=False)
                    elapsed_time = time.time() - self.init_start_time
                    self.logger.info(f"EasyOCR初始化成功，耗时: {elapsed_time:.2f}秒")
                    print(f"EasyOCR初始化成功，耗时: {elapsed_time:.2f}秒")
                    self.is_initialized = True
                    if callback:
                        callback(True, f"EasyOCR初始化成功，耗时: {elapsed_time:.1f}秒")
                        
                elif self.engine_type == "paddleocr":
                    self.logger.info("使用PaddleOCR初始化...")
                    self.ocr = PaddleOCR(lang='ch')
                    elapsed_time = time.time() - self.init_start_time
                    self.logger.info(f"PaddleOCR初始化成功，耗时: {elapsed_time:.2f}秒")
                    print(f"PaddleOCR初始化成功，耗时: {elapsed_time:.2f}秒")
                    self.is_initialized = True
                    if callback:
                        callback(True, f"PaddleOCR初始化成功，耗时: {elapsed_time:.1f}秒")
                else:
                    raise Exception("未找到可用的OCR引擎")
                    
            except Exception as e:
                elapsed_time = time.time() - self.init_start_time
                error_msg = f"OCR初始化失败，耗时: {elapsed_time:.1f}秒。错误: {str(e)}"
                self.logger.error(error_msg)
                print(error_msg)
                self.is_initialized = False
                if callback:
                    callback(False, error_msg)

        # 在后台线程中初始化
        thread = threading.Thread(target=init_worker, daemon=True)
        thread.start()
    
    def process_image(self, image_path):
        """处理图片并返回识别结果"""
        if not self.is_initialized or self.ocr is None:
            raise Exception("OCR引擎未初始化，请稍候...")

        start_time = time.time()
        self.logger.info(f"开始处理图片: {image_path}")
        
        try:
            # 检查图片文件
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")
                
            file_size = os.path.getsize(image_path)
            if file_size == 0:
                raise Exception("图片文件为空")
            elif file_size > 50 * 1024 * 1024:  # 50MB
                raise Exception("图片文件过大（超过50MB）")
                
            self.logger.info(f"图片文件大小: {file_size / 1024:.1f} KB")
            
            # 根据引擎类型处理
            if self.engine_type == "easyocr":
                result = self.ocr.readtext(image_path)
                texts = [item[1] for item in result if item[2] > 0.5]  # 置信度过滤
                
            elif self.engine_type == "paddleocr":
                result = self.ocr.ocr(image_path)
                texts = []
                if result and result[0]:
                    for line in result[0]:
                        if len(line) >= 2 and len(line[1]) >= 1:
                            texts.append(line[1][0])
            
            processing_time = time.time() - start_time
            self.logger.info(f"OCR识别完成，耗时: {processing_time:.2f}秒")

            if texts:
                result_text = '\n'.join(texts)
                self.logger.info(f"识别到 {len(texts)} 行文字")
                return result_text
            else:
                return "未识别到文字内容"
                
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"图片处理失败（耗时{processing_time:.1f}秒）: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)


class OCRApp:
    """OCR UI应用程序 - 轻量级版本"""

    def __init__(self):
        self.root = TkinterDnD.Tk()
        self.root.title(f"OCR文字识别工具 - 轻量级版本 ({OCR_ENGINE})")
        self.root.minsize(1000, 600)

        # 初始化OCR处理器
        self.ocr_processor = OCRProcessor()

        # 状态变量
        self.current_image_path = None
        self.is_processing = False
        self.ocr_ready = False
        self.init_start_time = None
        self.status_update_timer = None

        # 创建UI
        self.create_widgets()
        self.setup_drag_drop()
        self.setup_keyboard_shortcuts()

        # 异步初始化OCR
        self.start_ocr_initialization()

        # 居中显示
        self.center_window()

    def center_window(self):
        """将窗口居中显示"""
        try:
            window_width = 1200
            window_height = 700
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            self.root.wm_geometry(f"{window_width}x{window_height}+{x}+{y}")
            self.root.update_idletasks()
        except Exception:
            pass

    def start_ocr_initialization(self):
        """开始OCR初始化"""
        engine_name = "EasyOCR" if OCR_ENGINE == "easyocr" else "PaddleOCR"
        self.status_var.set(f"正在初始化{engine_name}引擎...")
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)
        
        # 更新拖拽区域提示
        if OCR_ENGINE == "easyocr":
            tip_text = f"🔄 正在初始化{engine_name}引擎...\n\n📦 首次运行需要下载模型（约50MB）\n⏱️ 预计需要1-3分钟\n\n💡 比PaddleOCR更轻量快速！"
        else:
            tip_text = f"🔄 正在初始化{engine_name}引擎...\n\n📦 首次运行需要下载模型（约300MB）\n⏱️ 预计需要3-10分钟\n\n💡 下载完成后使用会很快！"
            
        self.drop_area.configure(
            text=tip_text,
            bg="#FFF3E0",
            fg="#F57C00",
            font=("Arial", 11)
        )

        # 异步初始化OCR
        self.ocr_processor.initialize_ocr(self.on_ocr_initialized)
        
        # 启动状态更新定时器
        self.init_start_time = time.time()
        self.start_status_update_timer()

    def start_status_update_timer(self):
        """启动状态更新定时器"""
        self.update_initialization_status()
        
    def update_initialization_status(self):
        """更新初始化状态显示"""
        if not self.ocr_ready and self.init_start_time:
            elapsed = time.time() - self.init_start_time
            minutes = int(elapsed // 60)
            seconds = int(elapsed % 60)
            
            engine_name = "EasyOCR" if OCR_ENGINE == "easyocr" else "PaddleOCR"
            
            if elapsed < 30:
                status_msg = f"正在初始化{engine_name}... ({seconds}秒) - 检查本地模型"
                tip_msg = f"🔄 正在初始化{engine_name}...\n\n🔍 检查本地模型文件\n⏱️ 已用时: {seconds}秒"
            elif elapsed < 60:
                status_msg = f"正在下载{engine_name}模型... ({seconds}秒)"
                model_size = "50MB" if OCR_ENGINE == "easyocr" else "300MB"
                tip_msg = f"🔄 正在下载{engine_name}模型...\n\n📥 下载中，请保持网络连接\n📦 模型大小约{model_size}\n⏱️ 已用时: {seconds}秒\n\n💡 首次下载后使用会很快！"
            else:
                status_msg = f"正在下载{engine_name}模型... ({minutes}分{seconds}秒)"
                model_size = "50MB" if OCR_ENGINE == "easyocr" else "300MB"
                tip_msg = f"🔄 正在下载{engine_name}模型...\n\n📥 下载中，请耐心等待\n📦 模型大小约{model_size}\n⏱️ 已用时: {minutes}分{seconds}秒\n🌐 网络连接正常\n\n💡 下载完成后使用会很快！"
            
            self.status_var.set(status_msg)
            self.drop_area.configure(text=tip_msg)
            
            # 每2秒更新一次
            self.status_update_timer = self.root.after(2000, self.update_initialization_status)
    
    def stop_status_update_timer(self):
        """停止状态更新定时器"""
        if self.status_update_timer:
            self.root.after_cancel(self.status_update_timer)
            self.status_update_timer = None

    def on_ocr_initialized(self, success, message):
        """OCR初始化完成回调"""
        def update_ui():
            # 停止状态更新定时器
            self.stop_status_update_timer()
            
            self.progress_bar.stop()
            self.progress_bar.pack_forget()

            if success:
                self.ocr_ready = True
                self.status_var.set(f"OCR引擎初始化完成 - {message}")
                self.drop_area.configure(
                    text="📷 拖拽图片到此处\n📋 或按Ctrl+V粘贴\n🖱️ 或点击选择图片",
                    bg="#E8F5E8",
                    fg="#2E7D32"
                )
                
                # 计算总耗时
                if self.init_start_time:
                    total_time = time.time() - self.init_start_time
                    time_msg = f"总耗时: {total_time:.1f}秒"
                else:
                    time_msg = message
                
                # 显示成功消息
                engine_name = "EasyOCR" if OCR_ENGINE == "easyocr" else "PaddleOCR"
                messagebox.showinfo("初始化完成", f"🎉 {engine_name}引擎初始化成功！\n\n{time_msg}\n\n现在可以开始识别图片了！")
            else:
                self.status_var.set(f"OCR初始化失败: {message}")
                self.drop_area.configure(
                    text=f"❌ OCR初始化失败\n{message}\n\n点击重试",
                    bg="#FFEBEE",
                    fg="#C62828"
                )
                messagebox.showerror("初始化失败", f"OCR初始化失败：\n{message}")

        # 在主线程中更新UI
        self.root.after(0, update_ui)

    def create_widgets(self):
        """创建UI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 左侧框架（图片区域）
        left_frame = ttk.LabelFrame(main_frame, text="📷 图片区域 (支持拖拽、粘贴Ctrl+V、点击选择)", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 拖拽提示区域
        self.drop_area = tk.Label(
            left_frame,
            text="🔄 正在初始化OCR引擎...\n请稍候",
            bg="#E3F2FD",
            fg="#1976D2",
            font=("Arial", 14, "bold"),
            relief=tk.GROOVE,
            bd=3,
            cursor="hand2"
        )
        self.drop_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.drop_area.bind("<Button-1>", self.select_image)

        # 右侧框架（结果区域）
        right_frame = ttk.LabelFrame(main_frame, text="📝 识别结果", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(10, 0))
        right_frame.configure(width=400)

        # 文本显示区域
        self.text_area = scrolledtext.ScrolledText(
            right_frame,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 12),
            height=25,
            width=45,
            bg="#FAFAFA",
            fg="#333333",
            selectbackground="#2196F3",
            selectforeground="white"
        )
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 按钮框架
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X)

        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 8))

        # 复制按钮
        self.copy_button = ttk.Button(
            button_row1,
            text="📋 复制文本 (Ctrl+C)",
            command=self.copy_text,
            state=tk.DISABLED,
            width=20
        )
        self.copy_button.pack(side=tk.LEFT, padx=(0, 8))

        # 清空按钮
        self.clear_button = ttk.Button(
            button_row1,
            text="🗑️ 清空",
            command=self.clear_results,
            width=12
        )
        self.clear_button.pack(side=tk.LEFT)

        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X)

        # 粘贴按钮
        self.paste_button = ttk.Button(
            button_row2,
            text="📋 粘贴图片 (Ctrl+V)",
            command=self.paste_image,
            width=20
        )
        self.paste_button.pack(side=tk.LEFT, padx=(0, 8))

        # 选择文件按钮
        self.select_button = ttk.Button(
            button_row2,
            text="📁 选择文件",
            command=self.select_image,
            width=12
        )
        self.select_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            mode='indeterminate'
        )

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.drop_area.drop_target_register(DND_FILES)
        self.drop_area.dnd_bind('<<Drop>>', self.on_drop)

    def on_drop(self, event):
        """处理拖拽事件"""
        files = event.data.split()
        if files:
            file_path = files[0].strip('{}')
            self.process_dropped_file(file_path)

    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        self.root.bind('<Control-v>', lambda _: self.paste_image())
        self.root.bind('<Control-V>', lambda _: self.paste_image())
        self.root.bind('<Control-o>', lambda _: self.select_image())
        self.root.bind('<Control-O>', lambda _: self.select_image())
        self.root.bind('<Control-c>', lambda _: self.copy_text())
        self.root.bind('<Control-C>', lambda _: self.copy_text())
        self.root.bind('<Control-r>', lambda _: self.clear_results())
        self.root.bind('<Control-R>', lambda _: self.clear_results())
        self.root.bind('<Control-q>', lambda _: self.root.quit())
        self.root.bind('<Control-Q>', lambda _: self.root.quit())
        self.root.focus_set()

    def select_image(self, event=None):
        """选择图片文件"""
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.process_dropped_file(file_path)

    def process_dropped_file(self, file_path):
        """处理选择或拖拽的文件"""
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 检查文件类型
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in valid_extensions:
            messagebox.showerror("错误", "不支持的文件格式")
            return

        self.current_image_path = file_path
        self.display_image(file_path)
        self.start_ocr_processing(file_path)

    def display_image(self, image_path):
        """显示图片预览"""
        try:
            image = Image.open(image_path)
            display_size = (400, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            self.drop_area.configure(image=photo, text="")
            self.drop_area.image = photo
        except Exception as e:
            messagebox.showerror("错误", f"图片显示失败: {e}")

    def start_ocr_processing(self, image_path):
        """开始OCR处理（异步）"""
        self.is_processing = True
        self.status_var.set("正在识别文字...")
        self.copy_button.configure(state=tk.DISABLED)
        self.clear_button.configure(state=tk.DISABLED)

        # 显示进度条
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)

        # 清空之前的结果
        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(1.0, "正在识别中，请稍候...")

        # 在新线程中处理OCR
        thread = threading.Thread(
            target=self.ocr_worker,
            args=(image_path,),
            daemon=True
        )
        thread.start()

    def ocr_worker(self, image_path):
        """OCR工作线程"""
        try:
            result_text = self.ocr_processor.process_image(image_path)
            self.root.after(0, self.update_results, result_text, None)
        except Exception as e:
            self.root.after(0, self.update_results, None, str(e))

    def update_results(self, text, error):
        """更新识别结果"""
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        self.is_processing = False
        self.clear_button.configure(state=tk.NORMAL)

        if error:
            self.status_var.set(f"识别失败: {error}")
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, f"识别失败: {error}")
            messagebox.showerror("错误", f"OCR处理失败: {error}")
        else:
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, text)
            self.copy_button.configure(state=tk.NORMAL)
            self.status_var.set("识别完成")

    def copy_text(self):
        """复制文本到剪贴板"""
        text = self.text_area.get(1.0, tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.status_var.set("文本已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的文本")

    def clear_results(self):
        """清空结果"""
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，无法清空")
            return

        self.text_area.delete(1.0, tk.END)
        self.drop_area.configure(
            image="",
            text="📷 拖拽图片到此处\n📋 或按Ctrl+V粘贴\n🖱️ 或点击选择图片"
        )
        self.drop_area.image = None
        self.copy_button.configure(state=tk.DISABLED)
        self.current_image_path = None
        self.status_var.set("就绪")

    def paste_image(self):
        """从剪贴板粘贴图片"""
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        try:
            clipboard_data = ImageGrab.grabclipboard()
            if clipboard_data is None:
                messagebox.showwarning("提示", "剪贴板中没有图片数据")
                return

            # 保存临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_path = temp_file.name
            temp_file.close()

            clipboard_data.save(temp_path, 'PNG')
            self.current_image_path = temp_path
            self.display_image(temp_path)
            self.start_ocr_processing(temp_path)
            self.status_var.set("已粘贴图片，开始识别...")

        except Exception as e:
            messagebox.showerror("错误", f"粘贴图片失败: {e}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


if __name__ == "__main__":
    if OCR_ENGINE is None:
        print("错误：未找到OCR引擎")
        print("请安装以下任一OCR库：")
        print("1. pip install easyocr  (推荐，更轻量)")
        print("2. pip install paddlepaddle paddleocr")
        input("按回车键退出...")
    else:
        try:
            app = OCRApp()
            app.run()
        except Exception as e:
            print(f"程序启动失败: {e}")
            input("按回车键退出...")
