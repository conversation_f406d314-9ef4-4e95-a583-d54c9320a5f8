import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from tkinterdnd2 import DND_FILES, TkinterDnD
import threading
import os
import tempfile
from PIL import Image, ImageTk, ImageGrab
import pyperclip
from paddleocr import PaddleOCR


class OCRProcessor:
    """OCR处理器类"""

    def __init__(self):
        self.ocr = None
        self.is_initialized = False

    def initialize_ocr(self, callback=None):
        """异步初始化OCR引擎"""
        def init_worker():
            try:
                # 尝试使用新版本参数
                self.ocr = PaddleOCR(use_textline_orientation=True, lang='ch')
                print("OCR初始化成功（使用新版本参数）")
                self.is_initialized = True
                if callback:
                    callback(True, "OCR初始化成功")
            except Exception as e:
                print(f"新版本参数初始化失败: {e}")
                try:
                    # 尝试使用旧版本参数（兼容性）
                    self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
                    print("OCR初始化成功（使用旧版本参数）")
                    self.is_initialized = True
                    if callback:
                        callback(True, "OCR初始化成功")
                except Exception as e:
                    try:
                        # 最简单的初始化方式
                        self.ocr = PaddleOCR(lang='ch')
                        print("OCR初始化成功（基础模式）")
                        self.is_initialized = True
                        if callback:
                            callback(True, "OCR初始化成功")
                    except Exception as e:
                        self.is_initialized = False
                        if callback:
                            callback(False, f"OCR初始化失败: {e}")

        # 在后台线程中初始化
        thread = threading.Thread(target=init_worker, daemon=True)
        thread.start()
    
    def process_image(self, image_path):
        """处理图片并返回识别结果"""
        if not self.is_initialized or self.ocr is None:
            raise Exception("OCR引擎未初始化，请稍候...")

        try:
            # 尝试使用新版本的predict方法
            try:
                result = self.ocr.predict(image_path)
            except AttributeError:
                # 如果没有predict方法，使用旧版本的ocr方法
                result = self.ocr.ocr(image_path, cls=True)

            if result and result[0]:
                # 提取文本内容
                texts = []
                for line in result[0]:
                    if len(line) >= 2 and len(line[1]) >= 1:
                        texts.append(line[1][0])
                return '\n'.join(texts)
            else:
                return "未识别到文字内容"
        except Exception as e:
            raise Exception(f"图片处理失败: {e}")


class OCRApp:
    """OCR UI应用程序"""

    def __init__(self):
        self.root = TkinterDnD.Tk()
        self.root.title("OCR文字识别工具 - 支持拖拽、粘贴、点击选择")
        self.root.minsize(1000, 600)

        # 初始化OCR处理器（不立即初始化）
        self.ocr_processor = OCRProcessor()

        # 当前图片路径和处理状态
        self.current_image_path = None
        self.is_processing = False
        self.ocr_ready = False

        # 创建UI
        self.create_widgets()
        self.setup_drag_drop()
        self.setup_keyboard_shortcuts()

        # 异步初始化OCR
        self.start_ocr_initialization()

        # 立即尝试居中
        self.center_window()

        # 再次延迟居中，确保生效
        self.root.after(500, self.center_window)

    def center_window(self):
        """将窗口居中显示"""
        try:
            # 设置窗口大小
            window_width = 1200
            window_height = 700

            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # 计算居中位置
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            # 确保窗口不会超出屏幕边界
            if x < 0:
                x = 0
            if y < 0:
                y = 0

            # 使用wm_geometry方法设置位置
            self.root.wm_geometry(f"{window_width}x{window_height}+{x}+{y}")

            # 强制更新
            self.root.update_idletasks()

        except Exception as e:
            # 静默处理错误，不影响程序运行
            pass

    def start_ocr_initialization(self):
        """开始OCR初始化"""
        self.status_var.set("正在初始化OCR引擎，请稍候...")
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)

        # 异步初始化OCR
        self.ocr_processor.initialize_ocr(self.on_ocr_initialized)

    def on_ocr_initialized(self, success, message):
        """OCR初始化完成回调"""
        def update_ui():
            self.progress_bar.stop()
            self.progress_bar.pack_forget()

            if success:
                self.ocr_ready = True
                self.status_var.set("OCR引擎初始化完成，可以开始使用")
                self.drop_area.configure(
                    text="📷 拖拽图片到此处\n📋 或按Ctrl+V粘贴\n🖱️ 或点击选择图片",
                    bg="#F5F5F5",
                    fg="#666666"
                )
            else:
                self.status_var.set(f"OCR初始化失败: {message}")
                self.drop_area.configure(
                    text=f"OCR初始化失败\n{message}",
                    bg="lightcoral",
                    fg="darkred"
                )

        # 在主线程中更新UI
        self.root.after(0, update_ui)

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="选择图片 (Ctrl+O)", command=self.select_image)
        file_menu.add_command(label="粘贴图片 (Ctrl+V)", command=self.paste_image)
        file_menu.add_separator()
        file_menu.add_command(label="退出 (Ctrl+Q)", command=self.root.quit)

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="复制文本 (Ctrl+C)", command=self.copy_text)
        edit_menu.add_command(label="清空 (Ctrl+R)", command=self.clear_results)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        self.root.bind('<Control-v>', lambda e: self.paste_image())
        self.root.bind('<Control-V>', lambda e: self.paste_image())
        self.root.bind('<Control-o>', lambda e: self.select_image())
        self.root.bind('<Control-O>', lambda e: self.select_image())
        self.root.bind('<Control-c>', lambda e: self.copy_text())
        self.root.bind('<Control-C>', lambda e: self.copy_text())
        self.root.bind('<Control-r>', lambda e: self.clear_results())
        self.root.bind('<Control-R>', lambda e: self.clear_results())
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.bind('<Control-Q>', lambda e: self.root.quit())

        # 让窗口可以获得焦点以接收键盘事件
        self.root.focus_set()

    def create_widgets(self):
        """创建UI组件"""
        # 创建菜单栏
        self.create_menu()

        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 左侧框架（图片区域）- 占更多空间
        left_frame = ttk.LabelFrame(main_frame, text="📷 图片区域 (支持拖拽、粘贴Ctrl+V、点击选择)", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 拖拽提示区域 - 改进设计
        self.drop_area = tk.Label(
            left_frame,
            text="🔄 正在初始化OCR引擎...\n请稍候",
            bg="#E3F2FD",
            fg="#1976D2",
            font=("Arial", 14, "bold"),
            relief=tk.GROOVE,
            bd=3,
            cursor="hand2"
        )
        self.drop_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.drop_area.bind("<Button-1>", self.select_image)
        
        # 右侧框架（结果区域）- 调整宽度比例
        right_frame = ttk.LabelFrame(main_frame, text="📝 识别结果", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(10, 0))
        right_frame.configure(width=400)  # 固定宽度

        # 文本显示区域 - 改进样式
        self.text_area = scrolledtext.ScrolledText(
            right_frame,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 12),  # 更好的中文字体
            height=25,
            width=45,
            bg="#FAFAFA",
            fg="#333333",
            selectbackground="#2196F3",
            selectforeground="white"
        )
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 按钮框架 - 改进布局
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X)

        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 8))

        # 复制按钮 - 更大更明显
        self.copy_button = ttk.Button(
            button_row1,
            text="📋 复制文本 (Ctrl+C)",
            command=self.copy_text,
            state=tk.DISABLED,
            width=20
        )
        self.copy_button.pack(side=tk.LEFT, padx=(0, 8))

        # 清空按钮
        self.clear_button = ttk.Button(
            button_row1,
            text="🗑️ 清空",
            command=self.clear_results,
            width=12
        )
        self.clear_button.pack(side=tk.LEFT)

        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X)

        # 粘贴按钮
        self.paste_button = ttk.Button(
            button_row2,
            text="📋 粘贴图片 (Ctrl+V)",
            command=self.paste_image,
            width=20
        )
        self.paste_button.pack(side=tk.LEFT, padx=(0, 8))

        # 选择文件按钮
        self.select_button = ttk.Button(
            button_row2,
            text="📁 选择文件",
            command=self.select_image,
            width=12
        )
        self.select_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.root,
            variable=self.progress_var,
            mode='indeterminate'
        )

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.drop_area.drop_target_register(DND_FILES)
        self.drop_area.dnd_bind('<<Drop>>', self.on_drop)
    
    def on_drop(self, event):
        """处理拖拽事件"""
        files = event.data.split()
        if files:
            file_path = files[0].strip('{}')  # 移除可能的大括号
            self.process_dropped_file(file_path)
    
    def select_image(self, event=None):
        """选择图片文件"""
        # 忽略event参数，仅用于绑定事件
        _ = event

        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许选择新图片
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.process_dropped_file(file_path)
    
    def process_dropped_file(self, file_path):
        """处理选择或拖拽的文件"""
        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许处理新文件
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 检查文件类型
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext not in valid_extensions:
            messagebox.showerror("错误", "不支持的文件格式")
            return

        self.current_image_path = file_path
        self.display_image(file_path)
        self.start_ocr_processing(file_path)
    
    def display_image(self, image_path):
        """显示图片预览"""
        try:
            # 打开并调整图片大小
            image = Image.open(image_path)
            
            # 计算缩放比例
            display_size = (400, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            # 转换为tkinter可用的格式
            photo = ImageTk.PhotoImage(image)
            
            # 更新显示区域
            self.drop_area.configure(image=photo, text="")
            self.drop_area.image = photo  # 保持引用
            
        except Exception as e:
            messagebox.showerror("错误", f"图片显示失败: {e}")
    
    def start_ocr_processing(self, image_path):
        """开始OCR处理（异步）"""
        # 设置处理状态
        self.is_processing = True
        self.status_var.set("正在识别文字...")
        self.copy_button.configure(state=tk.DISABLED)
        self.clear_button.configure(state=tk.DISABLED)

        # 显示进度条
        self.progress_bar.pack(side=tk.BOTTOM, fill=tk.X, before=self.status_bar)
        self.progress_bar.start(10)  # 开始动画

        # 清空之前的结果
        self.text_area.delete(1.0, tk.END)
        self.text_area.insert(1.0, "正在识别中，请稍候...")

        # 在新线程中处理OCR
        thread = threading.Thread(
            target=self.ocr_worker,
            args=(image_path,),
            daemon=True
        )
        thread.start()
    
    def ocr_worker(self, image_path):
        """OCR工作线程"""
        try:
            result_text = self.ocr_processor.process_image(image_path)
            # 在主线程中更新UI
            self.root.after(0, self.update_results, result_text, None)
        except Exception as e:
            self.root.after(0, self.update_results, None, str(e))
    
    def update_results(self, text, error):
        """更新识别结果"""
        # 停止进度条并隐藏
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

        # 重置处理状态
        self.is_processing = False
        self.clear_button.configure(state=tk.NORMAL)

        if error:
            self.status_var.set(f"识别失败: {error}")
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, f"识别失败: {error}")
            messagebox.showerror("错误", f"OCR处理失败: {error}")
        else:
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, text)
            self.copy_button.configure(state=tk.NORMAL)
            self.status_var.set("识别完成")
    
    def copy_text(self):
        """复制文本到剪贴板"""
        text = self.text_area.get(1.0, tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.status_var.set("文本已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的文本")
    
    def clear_results(self):
        """清空结果"""
        # 如果正在处理，不允许清空
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，无法清空")
            return

        self.text_area.delete(1.0, tk.END)
        self.drop_area.configure(
            image="",
            text="📷 拖拽图片到此处\n📋 或按Ctrl+V粘贴\n🖱️ 或点击选择图片"
        )
        self.drop_area.image = None
        self.copy_button.configure(state=tk.DISABLED)
        self.current_image_path = None
        self.status_var.set("就绪")

    def paste_image(self):
        """从剪贴板粘贴图片"""
        # 检查OCR是否已初始化
        if not self.ocr_ready:
            messagebox.showwarning("提示", "OCR引擎还未初始化完成，请稍候...")
            return

        # 如果正在处理，不允许粘贴
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请稍候...")
            return

        try:
            # 从剪贴板获取数据
            clipboard_data = ImageGrab.grabclipboard()

            if clipboard_data is None:
                messagebox.showwarning("提示", "剪贴板中没有图片数据")
                return

            # 检查剪贴板数据类型
            if isinstance(clipboard_data, list):
                # 如果是文件路径列表，尝试处理第一个图片文件
                if clipboard_data and len(clipboard_data) > 0:
                    file_path = clipboard_data[0]
                    if os.path.exists(file_path):
                        # 检查是否为图片文件
                        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
                        file_ext = os.path.splitext(file_path)[1].lower()
                        if file_ext in valid_extensions:
                            self.process_dropped_file(file_path)
                            return
                        else:
                            messagebox.showwarning("提示", "剪贴板中的文件不是支持的图片格式")
                            return
                    else:
                        messagebox.showwarning("提示", "剪贴板中的文件路径无效")
                        return
                else:
                    messagebox.showwarning("提示", "剪贴板中没有有效的文件数据")
                    return

            # 检查是否为PIL图片对象
            if not hasattr(clipboard_data, 'save'):
                messagebox.showwarning("提示", "剪贴板中的数据不是有效的图片格式")
                return

            # 保存临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # 保存图片到临时文件
            clipboard_data.save(temp_path, 'PNG')

            # 处理图片
            self.current_image_path = temp_path
            self.display_image(temp_path)
            self.start_ocr_processing(temp_path)

            self.status_var.set("已粘贴图片，开始识别...")

        except Exception as e:
            messagebox.showerror("错误", f"粘贴图片失败: {e}")

    def show_help(self):
        """显示使用说明"""
        help_text = """
OCR文字识别工具 - 使用说明

📷 图片输入方式：
• 拖拽：直接将图片文件拖拽到左侧区域
• 粘贴：按Ctrl+V粘贴剪贴板中的图片
• 选择：点击左侧区域或按Ctrl+O选择文件

⌨️ 快捷键：
• Ctrl+V：粘贴图片
• Ctrl+O：选择图片文件
• Ctrl+C：复制识别结果
• Ctrl+R：清空结果
• Ctrl+Q：退出程序

📝 支持格式：
• 图片格式：JPG, PNG, BMP, GIF, TIFF
• 文字语言：中文、英文等多种语言

💡 使用技巧：
• 图片清晰度越高，识别效果越好
• 文字对比度高的图片识别更准确
• 支持截图工具直接粘贴
• 首次运行需要下载模型，请耐心等待
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("500x600")
        help_window.resizable(False, False)

        # 居中显示
        help_window.transient(self.root)
        help_window.grab_set()

        text_widget = scrolledtext.ScrolledText(
            help_window,
            wrap=tk.WORD,
            font=("Microsoft YaHei", 11),
            padx=20,
            pady=20
        )
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(1.0, help_text)
        text_widget.configure(state=tk.DISABLED)

        # 关闭按钮
        close_btn = ttk.Button(
            help_window,
            text="关闭",
            command=help_window.destroy
        )
        close_btn.pack(pady=10)

    def show_about(self):
        """显示关于对话框"""
        about_text = """
OCR文字识别工具 v1.0

基于PaddleOCR开发的图片文字识别工具

✨ 主要特性：
• 支持多种图片输入方式
• 高精度文字识别
• 简洁易用的界面
• 丰富的快捷键支持

🔧 技术栈：
• PaddleOCR - 文字识别引擎
• Tkinter - 用户界面
• PIL - 图片处理

📧 如有问题或建议，欢迎反馈！
        """

        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


if __name__ == "__main__":
    try:
        app = OCRApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
