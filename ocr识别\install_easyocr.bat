@echo off
chcp 65001 >nul
echo ========================================
echo OCR识别工具 - EasyOCR安装脚本
echo ========================================
echo.
echo 正在安装EasyOCR（轻量级OCR引擎）...
echo 这比PaddleOCR更快更轻量，推荐使用！
echo.

echo 1. 更新pip...
python -m pip install --upgrade pip

echo.
echo 2. 安装EasyOCR...
pip install easyocr

echo.
echo 3. 安装其他依赖...
pip install pillow pyperclip tkinterdnd2

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 现在可以运行 ocr_ui_lite.py 使用轻量级OCR了！
echo 首次运行会下载约50MB的模型文件。
echo.
pause
